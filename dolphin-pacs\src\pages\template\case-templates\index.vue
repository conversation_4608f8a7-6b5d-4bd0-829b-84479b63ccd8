<template>
  <div class="case-templates-page">
   
    <div class="templates-container">
      <div
        v-for="template in templates"
        :key="template.id"
        class="template-card"
        @mouseenter="hoveredCard = template.id"
        @mouseleave="hoveredCard = null"
        @click="handleTemplateClick(template)"
      >
        <!-- 卡片内容 -->
        <div class="card-content">
          <!-- 分类标签 -->
          <div class="category-tag">
            {{ template.category }}
          </div>

          <!-- 模板信息 -->
          <div class="template-info">
            <h3 class="template-name">{{ template.name }}</h3>
            <p class="template-description">{{ template.description }}</p>
          </div>

          <!-- 按钮组 -->
          <transition name="fade">
            <div v-show="hoveredCard === template.id" class="action-buttons">
              <el-button
                type="primary"
                size="small"
                @click.stop="handlePreview(template)"
                class="action-btn preview-btn"
              >
                预览
              </el-button>
              <el-button
                type="success"
                size="small"
                @click.stop="handleUseTemplate(template)"
                class="action-btn use-btn"
              >
                使用模板
              </el-button>
            </div>
          </transition>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

defineOptions({
  name: "CaseTemplates"
})

const router = useRouter()

// 悬浮状态
const hoveredCard = ref<string | null>(null)

// 模板数据接口
interface Template {
  id: string
  name: string
  description: string
  category: string
  downloadUrl?: string
}

// 模拟模板数据
const templates = ref<Template[]>([
  {
    id: '1',
    name: '心脏超声检查报告',
    description: '适用于心脏超声检查的标准报告模板',
    category: '心血管',
    downloadUrl: '/downloads/cardiac-template.docx'
  },
  {
    id: '2',
    name: '腹部超声检查报告',
    description: '适用于腹部器官超声检查的报告模板',
    category: '消化系统',
    downloadUrl: '/downloads/abdominal-template.docx'
  },
  {
    id: '3',
    name: '甲状腺超声检查报告',
    description: '专门用于甲状腺超声检查的报告模板',
    category: '内分泌',
    downloadUrl: '/downloads/thyroid-template.docx'
  },
  {
    id: '4',
    name: '产科超声检查报告',
    description: '适用于孕期超声检查的专业报告模板',
    category: '妇产科',
    downloadUrl: '/downloads/obstetric-template.docx'
  },
  {
    id: '5',
    name: '血管超声检查报告',
    description: '用于血管系统超声检查的报告模板',
    category: '血管外科',
    downloadUrl: '/downloads/vascular-template.docx'
  },
  {
    id: '6',
    name: '肌骨超声检查报告',
    description: '适用于肌肉骨骼系统超声检查的模板',
    category: '骨科',
    downloadUrl: '/downloads/musculoskeletal-template.docx'
  }
])

// 点击卡片进入编辑器
const handleTemplateClick = (template: Template) => {
  router.push({
    name: 'TemplateEditor',
    params: { id: template.id },
    query: { name: template.name }
  })
}

// 预览模板
const handlePreview = (template: Template) => {
  ElMessage.info(`预览模板：${template.name}`)
  // TODO: 实现预览功能，可能是弹窗或跳转到预览页面
  console.log('预览模板:', template)
}

// 使用模板
const handleUseTemplate = (template: Template) => {
  router.push({
    name: 'TemplateEditor',
    params: { id: template.id },
    query: { name: template.name }
  })
}
</script>

<style lang="scss" scoped>
.case-templates-page {
  padding: 24px;
  min-height: 100vh;
  background-color: var(--el-bg-color-page);
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
  
  h2 {
    font-size: 28px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin-bottom: 8px;
  }
  
  .page-description {
    font-size: 16px;
    color: var(--el-text-color-regular);
    margin: 0;
  }
}

.templates-container {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  justify-content: center;
  max-width: 1400px;
  margin: 0 auto;
}

.template-card {
  position: relative;
  width: 300px;
  height: 200px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: var(--el-color-primary);
  }
}

.card-content {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.category-tag {
  display: inline-block;
  background: var(--el-color-primary);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 16px;
  align-self: flex-start;
}

.template-info {
  .template-name {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0 0 8px 0;
    line-height: 1.4;
  }

  .template-description {
    font-size: 14px;
    color: var(--el-text-color-regular);
    line-height: 1.5;
    margin: 0;
  }
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  margin-top: 16px;

  .action-btn {
    flex: 1;
    border-radius: 8px;
    font-weight: 500;
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 响应式布局
@media screen and (max-width: 1400px) {
  .templates-container {
    justify-content: flex-start;
  }
}

@media screen and (max-width: 768px) {
  .case-templates-page {
    padding: 16px;
  }
  
  .page-header {
    margin-bottom: 24px;
    
    h2 {
      font-size: 24px;
    }
    
    .page-description {
      font-size: 14px;
    }
  }
  
  .templates-container {
    gap: 16px;
    justify-content: center;
  }

  .template-card {
    width: 280px;
  }
}
</style>